#!/usr/bin/env python3
"""
测试通知处理器的集成功能
"""

import json
import logging
from unittest.mock import Mock, MagicMock
from dataclasses import dataclass
from typing import Dict, Any, List

# 设置基本的日志配置
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 模拟依赖
@dataclass
class MockAuthorWork:
    """模拟作者作品数据"""
    title: str = "测试作品标题"
    author_name: str = "测试作者"
    like_count: int = 0
    comment_count: int = 0
    share_count: int = 0
    collect_count: int = 0

@dataclass
class MockWorkDetailTaskModel:
    """模拟作品任务模型"""
    work_id: str = "test_work_123"
    work_url: str = "https://example.com/work/123"
    platform: str = "xhs"
    threshold: str = "否"
    author_work: MockAuthorWork = None
    submit_user: List[Dict] = None
    extends: str = "test_extends"
    
    def __post_init__(self):
        if self.author_work is None:
            self.author_work = MockAuthorWork()
        if self.submit_user is None:
            self.submit_user = [{"unionId": "test_union_id_123"}]

class MockPipelineContext:
    """模拟Pipeline上下文"""
    def __init__(self):
        self.task_list = []
        self.user_info = {"user_id": 123, "company": "水云兔"}
        self.notifier = Mock()
        self.notifier.send_message = Mock()

class MockClientConfig:
    """模拟客户端配置"""
    pass

class MockNotifyRecordRepo:
    """模拟通知记录仓库"""
    def insert(self, user_id, empty_str, submit_users_json, work_id, platform):
        logger.info(f"Mock insert notification record: user_id={user_id}, work_id={work_id}")

def test_notify_handler():
    """测试通知处理器功能"""
    try:
        # 导入通知处理器
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from core.handlers.notify_handler import NotifyHandler
        
        # 创建模拟对象
        client_config = MockClientConfig()
        notify_handler = NotifyHandler(client_config, logger)
        
        # 替换依赖为模拟对象
        notify_handler.task_notify_record_repo = MockNotifyRecordRepo()
        
        # 创建测试数据
        context = MockPipelineContext()
        
        # 测试案例1: 水云兔公司，点赞数达到阈值
        work1 = MockWorkDetailTaskModel()
        work1.author_work.like_count = 60  # 超过50的阈值
        context.task_list = [work1]
        
        logger.info("=== 测试案例1: 水云兔公司，点赞数达到阈值 ===")
        result_context = notify_handler.handle(context)
        
        # 验证通知器被调用
        context.notifier.send_message.assert_called()
        logger.info("✅ 测试案例1通过：通知器被正确调用")
        
        # 测试案例2: 点赞数未达到阈值
        context.notifier.send_message.reset_mock()
        work2 = MockWorkDetailTaskModel()
        work2.author_work.like_count = 30  # 未达到50的阈值
        context.task_list = [work2]
        
        logger.info("=== 测试案例2: 点赞数未达到阈值 ===")
        result_context = notify_handler.handle(context)
        
        # 验证通知器未被调用
        context.notifier.send_message.assert_not_called()
        logger.info("✅ 测试案例2通过：未达到阈值时不发送通知")
        
        # 测试案例3: 告趣公司，不同阈值
        context.notifier.send_message.reset_mock()
        context.user_info["company"] = "告趣"
        work3 = MockWorkDetailTaskModel()
        work3.author_work.like_count = 250  # 超过告趣的200阈值
        context.task_list = [work3]
        
        logger.info("=== 测试案例3: 告趣公司，点赞数达到阈值 ===")
        result_context = notify_handler.handle(context)
        
        # 验证通知器被调用
        context.notifier.send_message.assert_called()
        logger.info("✅ 测试案例3通过：告趣公司阈值检查正确")
        
        logger.info("🎉 所有测试案例通过！通知处理器集成成功！")
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_notify_handler()
