#!/usr/bin/env python3
"""
测试配置修复和核心功能
"""

import sys
import tempfile
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append('.')


def test_client_config_from_yaml():
    """测试 ClientConfig.from_yaml 方法"""
    
    print("=== 测试 ClientConfig.from_yaml 方法 ===")
    
    # 创建临时 YAML 文件
    yaml_content = """
name: "测试客户"

schedule:
  cron: "0 */2 * * *"
  max_tasks: 100

data_fetcher:
  source_type: "dingtalk_bitable"
  filter_rules:
    update_times_limit: 5

user_info:
  user_id: 118
  api_token: "test_token"

bitable:
  app_key: "test_app_key"
  app_secret: "test_app_secret"
  agent_id: "test_agent_id"
  webhook: "https://example.com/webhook"
"""
    
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False, encoding='utf-8') as f:
            f.write(yaml_content)
            temp_yaml_path = f.name
        
        # 测试 from_yaml 方法
        from config.client_config import ClientConfig
        
        config = ClientConfig.from_yaml(temp_yaml_path)
        
        print(f"✅ 成功加载配置: {config.name}")
        print(f"  - 调度配置: {config.schedule}")
        print(f"  - 数据获取配置: {config.data_fetcher}")
        print(f"  - 用户信息: {config.user_info}")
        print(f"  - 钉钉配置: {config.bitable}")
        
        # 验证配置内容
        assert config.name == "测试客户"
        assert config.schedule.get("cron") == "0 */2 * * *"
        assert config.user_info.get("user_id") == 118
        assert config.bitable.get("app_key") == "test_app_key"
        
        print("✅ 配置内容验证通过")
        
        # 清理临时文件
        os.unlink(temp_yaml_path)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        # 清理临时文件
        if 'temp_yaml_path' in locals():
            try:
                os.unlink(temp_yaml_path)
            except:
                pass
        return False


def test_data_handler_structure():
    """测试 DataHandler 结构"""
    
    print("\n=== 测试 DataHandler 结构 ===")
    
    try:
        # 测试导入
        from core.handlers.data_handler import DataHandler, DataFetcher
        
        print("✅ DataHandler 和 DataFetcher 导入成功")
        
        # 检查关键方法
        data_fetcher_methods = [
            'fetch_data',
            '_query_format_task_list', 
            '_query_update_table_one',
            '_query_tasks_rows',
            '_extract_task_item',
            '_url_redirection'
        ]
        
        for method_name in data_fetcher_methods:
            if hasattr(DataFetcher, method_name):
                print(f"  ✅ {method_name} 方法存在")
            else:
                print(f"  ❌ {method_name} 方法缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ DataHandler 测试失败: {e}")
        return False


def test_webhook_sync_handler():
    """测试 WebhookSyncHandler"""
    
    print("\n=== 测试 WebhookSyncHandler ===")
    
    try:
        from core.handlers.sync_handler import WebhookSyncHandler
        
        print("✅ WebhookSyncHandler 导入成功")
        
        # 检查关键方法
        webhook_methods = [
            '_perform_batch_sync',
            '_send_sync_batch',
            '_format_webhook_data',
            '_convert_syt_format',
            '_convert_zz_format',
            '_convert_jyy_format',
            '_convert_gq_format',
            '_send_webhook_request'
        ]
        
        for method_name in webhook_methods:
            if hasattr(WebhookSyncHandler, method_name):
                print(f"  ✅ {method_name} 方法存在")
            else:
                print(f"  ❌ {method_name} 方法缺失")
        
        return True
        
    except Exception as e:
        print(f"❌ WebhookSyncHandler 测试失败: {e}")
        return False


def test_processor_structure():
    """测试 Processor 结构"""
    
    print("\n=== 测试 Processor 结构 ===")
    
    try:
        # 检查文件是否存在
        processor_file = Path("scense/shuiyuntu/syt_processor.py")
        
        if processor_file.exists():
            print("✅ syt_processor.py 文件存在")
            
            # 读取文件内容检查关键类和方法
            with open(processor_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            required_elements = [
                'class ShuiyuntuProcessor',
                'def __init__',
                'def _init_dingtalk_notifier',
                'def _build_processing_chain',
                'def process',
                'def create_shuiyuntu_processor',
                'def run_shuiyuntu_process'
            ]
            
            for element in required_elements:
                if element in content:
                    print(f"  ✅ {element} 存在")
                else:
                    print(f"  ❌ {element} 缺失")
            
            return True
        else:
            print("❌ syt_processor.py 文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ Processor 结构测试失败: {e}")
        return False


def test_configuration_file():
    """测试配置文件"""
    
    print("\n=== 测试配置文件 ===")
    
    config_file = Path("config/clients/shuiyuntu.yaml")
    
    if config_file.exists():
        print("✅ 水云兔配置文件存在")
        
        try:
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            required_sections = [
                'name',
                'schedule', 
                'data_fetcher',
                'work_updater',
                'notification',
                'user_info',
                'bitable'
            ]
            
            for section in required_sections:
                if section in config_data:
                    print(f"  ✅ {section} 配置存在")
                else:
                    print(f"  ❌ {section} 配置缺失")
            
            return True
            
        except Exception as e:
            print(f"❌ 配置文件解析失败: {e}")
            return False
    else:
        print("❌ 水云兔配置文件不存在")
        return False


if __name__ == "__main__":
    print("🚀 开始测试配置修复和核心功能...")
    
    tests = [
        test_client_config_from_yaml,
        test_data_handler_structure,
        test_webhook_sync_handler,
        test_processor_structure,
        test_configuration_file
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 发生异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("✅ ClientConfig.from_yaml 方法已修复")
        print("✅ DataHandler 数据获取逻辑完整")
        print("✅ WebhookSyncHandler 同步逻辑完整")
        print("✅ ShuiyuntuProcessor 处理器完整")
        print("✅ 配置文件结构正确")
    else:
        print("⚠️ 部分测试未通过，请检查相关实现")
