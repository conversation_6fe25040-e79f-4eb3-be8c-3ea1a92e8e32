
name: "水云兔"

# 调度配置
schedule:
  cron: "0 */2 * * *"
  max_tasks: 100

# 数据获取配置
data_fetcher:
  source_type: "dingtalk_bitable"  # 支持: dingtalk_bitable, service_client
  filter_rules:
    update_times_limit: 5
    platform: "all"

# 作品更新配置
work_updater:
  platforms: ["xhs", "dy"]
  batch_size: 100


# 通知配置
notification:
  use_database: true
  threshold_rules:
    "水云兔":
      like_count: 100    # 点赞数阈值
      comment_count: 30  # 评论数阈值
      share_count: 20    # 分享数阈值
    default:
      like_count: 200
      comment_count: 50
      share_count: 30
  message_templates:  # 消息模板配置
    "水云兔": "🚨 作品阈值通知：标题={title}, 作者={author_name}, 平台={platform}, {threshold_info}, 作品链接={work_url}"
    default: "作品阈值通知：{title} - {author_name} ({platform})"
  default_recipients:  # 默认通知接收人
    - unionId: "admin-shuiyuntu-1"
    - unionId: "admin-shuiyuntu-2"


# 用户信息配置
user_info:
  api-token: "api-token-fa384bc631772a3862dd7e305860492d"
  user_id: 118
  tenant: "7099e76b-6c94-4de0-9493-6c84d86b60e4"

bitable:
  app_key: "dinge7tittllm4o11z6b",
  app_secret: "OelnczysPJEzunL1n4IT94VCbPoqkBMXoLa1fQ048q3ebMSJLT4QZt5I5RV1iIp4",
  agent_id: "3912292254"
  webhook: "https://oapi.dingtalk.com/robot/send?access_token=1fe8be2a320a14ddd858c56eef93b4dc1f1349f51cce7134a694b410f297c3b6"
