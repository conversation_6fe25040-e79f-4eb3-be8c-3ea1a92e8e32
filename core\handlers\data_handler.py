"""
数据获取Handler - 负责从数据源获取待处理的作品数据

支持多种数据获取方式：
1. 从服务器查询 (需要api_token) - 参考 old/xhs_detail_updater.py 的 __query_tasks_rows 方法
2. 从多维表获取 (使用jss-api-extend) - 参考 jss_api_extend/bitable/base_dingtalk_bitable_notifier.py 的 query_records 方法
3. 客户特定的数据获取器 - 支持每个客户的个性化需求

架构设计：
- DataHandler: 基础数据获取处理器
- ClientSpecificDataFetcher: 客户特定的数据获取器接口
- 具体实现类：为每个客户提供定制化的数据获取逻辑
"""
import json
from typing import List, Optional

import requests

from config.client_config import ClientConfig
from dao.model.work_detail_refresh import WorkDetailRefreshTask
from dao.repository.xhs_work_detail_refresh_repository import XhsWorkDetailRefreshRepository
from jss_api_extend import ServiceClient, Environment
from models.work_models import WorkDetailTaskModel
from utils.url_utils import UrlUtils
from .base_handler import BaseHandler
from ..exceptions import DataFetchException
from ..pipeline_context import PipelineContext


class DataFetcher:
    """
    这里是用户获取需要更新数据的类，可以有多种方式获取，但是最终返回的就是 taskModel
    不同的用户可以实现不同的 获取数据的逻辑
    """
    def __init__(self, client_config: ClientConfig, logger_):
        self.client_config = client_config
        self.logger_ = logger_
        self.service_client = ServiceClient(
            Environment.LOCAL, 
            client_config.user_info.get("api_token"),
            logger_
        )
        
    def fetch_data(data_source_type: int) -> Optional[List[WorkDetailTaskModel]]:
        """
          taskModel
        """
        pass


    def _query_data_table(user_id: int, api_token: str):
        """
            从数据库查询可更新的表，再从表中查询需要更新的数据
        """
        pass

    def _query_data_from_service(user_id: int, api_token: str):
        """
            直接从服务端获取的数据
        """
        pass



class DataHandler(BaseHandler):

    def __init__(self, client_config: ClientConfig, logger_):
        super().__init__(client_config, logger_)
        self.logger_ = logger_
        self.refresh_repository = XhsWorkDetailRefreshRepository()
        self.data_fetcher = DataFetcher(client_config=client_config, logger_=logger_)


    def handle(self, context: PipelineContext) -> PipelineContext:

        """
            处理数据获取逻辑
        """

        try:
            self.logger.info(f"Starting data fetch for client: {context.client_name}")

            # 获取任务
            task_list = self.data_fetcher.fetch_data(data_source_type = context.data_source_type)

            _error_format_url = context.error_format_url_list

            for task in task_list:
                result = self.__extract_task_item(work_url=task.work_url)
                if result is None:
                    _error_format_url.append(task.work_url)
                    continue

                platform, work_id = result
                if not platform or not work_id:
                    self.logger_.warning(f"提取到的 platform 或 work_id 为空，跳过。Platform: {platform}, WorkId: {work_id}\n")
                    continue
                task.work_id = work_id
                task.platform = platform

            context.error_format_url_list = _error_format_url

            self._insert_tasks_to_db_batch(context, task_list)

            context.task_list = task_list if task_list else []

            self.logger.info(f"Fetched {len(context.task_list)} tasks for processing")
            return self.handle_next(context)

        except Exception as e:
            self.logger.error(f"Data fetch failed: {e}")
            raise DataFetchException(f"Failed to fetch data: {str(e)}")


    def _insert_tasks_to_db_batch(self,context: PipelineContext, tasks: List[WorkDetailTaskModel]) -> Optional[int]:
        """
        将任务插入到数据库

        Args:
            tasks: 任务列表
        """

        try:
            _task_repo_model_list: List[WorkDetailRefreshTask] = []
            for task in tasks:
                _task_repo_model = self._covert_task_to_repo_model(context,task)
                if not _task_repo_model:
                    continue

                _task_repo_model_list.append(_task_repo_model)
            task_num: int = self.refresh_repository.batch_insert_refresh_tasks(_task_repo_model_list)
            self.logger_.info(f"=== 成功插入任务 {task_num} 条 ===")
            return task_num
        except Exception as e:
            self.logger_.error(f"插入任务到数据库异常: {e}")
            raise

    def _covert_task_to_repo_model(self, context: PipelineContext, task: WorkDetailTaskModel) \
            -> Optional[WorkDetailRefreshTask]:
        """
        task model 转为数据存储
        :param task:
        :return:
        """
        try:
            return WorkDetailRefreshTask(
                id=None,
                record_id=context.batch_id,
                user_id=context.user_id,
                work_url=task.work_url,
                work_id=task.work_id,
                platform=task.platform,
                row_id=task.row_id,
                table_info=json.dumps({
                    "id_or_name": task.id_or_name,
                    "dentry_uuid": task.dentry_uuid
                })
            )
        except Exception as e:
            self.logger_.error(f"转换任务失败: {task}, 错误: {e}")
            return None


    def __extract_task_item(self, work_url: str):
        """
        提取链接内信息：链接转化， work_id 提取， platform 信息提取
        @param work_url:
        @return:
        """
        if not work_url:
            self.logger_.error(f"work_url为空")
            return None

        _origin_url = work_url
        _normal_url = _origin_url

        self.logger_.info(f"原始 work_url: {work_url}")

        # 分享链接确认
        if UrlUtils.check_short_url(_normal_url):
            _normal_url = self.__url_redirection(_normal_url)

        if not _normal_url:
            self.logger_.error(f"链接转化失败，短链转长连接失败 :{work_url}")
            return None

        #去空格
        _normal_url = _normal_url.strip()

        # 提取平台信息
        _platform: str = UrlUtils.select_platform(_normal_url)

        if not _platform:
            self.logger_.error(f"format fail message :{_origin_url}")
            return None

        if _platform == "xhs":
            _work_id = UrlUtils.get_xhs_work_id(_normal_url)
        else:
            _work_id = UrlUtils.get_dy_work_id(_normal_url)

        return _platform, _work_id

    def __url_redirection(self, _work_url: str) -> Optional[str]:
        """
        对短链的处理
        @param work_url:
        @return:
        """
        try:
            _response = requests.get(_work_url, allow_redirects=True, timeout=30)
            return _response.url
        except Exception as e:
            self.logger_.error(f"{e}")
            return None