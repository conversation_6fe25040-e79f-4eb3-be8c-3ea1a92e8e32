"""
数据获取Handler - 负责从数据源获取待处理的作品数据

支持多种数据获取方式：
1. 从服务器查询 (需要api_token) - 参考 old/xhs_detail_updater.py 的 __query_tasks_rows 方法
2. 从多维表获取 (使用jss-api-extend) - 参考 jss_api_extend/bitable/base_dingtalk_bitable_notifier.py 的 query_records 方法
3. 客户特定的数据获取器 - 支持每个客户的个性化需求

架构设计：
- DataHandler: 基础数据获取处理器
- ClientSpecificDataFetcher: 客户特定的数据获取器接口
- 具体实现类：为每个客户提供定制化的数据获取逻辑
"""

import json
import logging
from typing import List, Optional, Dict, Any

import requests

from config.client_config import ClientConfig
from dao.model.work_detail_refresh import WorkDetailRefreshTask
from dao.repository.xhs_work_detail_refresh_repository import XhsWorkDetailRefreshRepository
from jss_api_extend import ServiceClient, Environment
from models.work_models import WorkDetailTaskModel
from utils.url_utils import UrlUtils
from utils.time_utils import TimeUtils
from .base_handler import BaseHandler
from ..exceptions import DataFetchException
from ..pipeline_context import PipelineContext


class DataFetcher:
    """
    数据获取器 - 负责从不同数据源获取待更新的作品数据

    参考 old/xhs_detail_updater.py 的 __query_format_task_list 方法实现
    支持多种数据获取方式：
    1. 从服务器查询表信息，再从多维表获取数据
    2. 直接从服务端获取数据
    """

    def __init__(self, client_config: ClientConfig, logger, notifier):
        self.client_config = client_config
        self.logger = logger
        self.notifier = notifier
        self.service_client = ServiceClient(
            Environment.LOCAL, client_config.user_info.get("api_token"), logger
        )
        self.covert_fail_queue = []  # 转换失败的URL队列

    def fetch_data(
        self, record_id: str, data_source_type: int = 1
    ) -> Optional[List[WorkDetailTaskModel]]:
        """
        获取待更新的任务数据

        Args:
            record_id: 批次记录ID
            data_source_type: 数据源类型 (1: 从服务器查询表信息, 2: 直接从服务端获取)

        Returns:
            任务列表
        """
        try:
            if data_source_type == 1:
                # 从服务器查询表信息，再从多维表获取数据 (参考 __query_format_task_list)
                return self._query_format_task_list(record_id)
            elif data_source_type == 2:
                # 直接从服务端获取数据 (参考 __query_tasks_rows)
                return self._query_tasks_rows(platform="all", limit=100, record_id=record_id)
            else:
                self.logger.error(f"不支持的数据源类型: {data_source_type}")
                return None

        except Exception as e:
            self.logger.error(f"获取数据失败: {e}")
            return None

    def _query_format_task_list(self, record_id: str) -> Optional[List[WorkDetailTaskModel]]:
        """
        构建任务列表 - 参考 old/xhs_detail_updater.py 的 __query_format_task_list 方法

        Args:
            record_id: 批次记录ID

        Returns:
            任务列表
        """
        # 1. 查询表信息
        table_info = self._query_update_table_one()
        if not table_info:
            return None

        dentry_uuid = table_info.get("dentryUuid")
        id_or_name = table_info.get("idOrName")
        max_update_times = table_info.get("updateTimes")

        if not all([dentry_uuid, id_or_name, max_update_times]):
            self.logger.error(f"服务端返回数据不完整 返回: {table_info}")
            return None

        self.logger.info(
            f"开始更新: dentry_uuid：{dentry_uuid}, id_or_name：{id_or_name}, max_update_times：{max_update_times}"
        )

        # 2. 从多维表获取记录列表
        record_list = self.notifier.list_bitable_data_by_api_filter(
            dentry_uuid, id_or_name, max_update_times
        )

        if not record_list:
            self.logger.error(f"数据表 :{dentry_uuid} 无数据")
            return None

        # 3. 构建任务列表
        task_list = []
        for record in record_list:
            # 提取任务信息
            result = self._extract_task_item(work_url=record.work_url)
            if result is None:
                self.covert_fail_queue.append(record.work_url)
                continue

            platform, work_id = result
            if not platform or not work_id:
                self.logger.warning(
                    f"提取到的 platform 或 work_id 为空，跳过。Platform: {platform}, WorkId: {work_id}"
                )
                continue

            # 创建任务模型
            work_task = WorkDetailTaskModel(
                id=None,
                work_url=record.work_url,
                platform=platform,
                work_id=work_id,
                row_id=record.row_id,
                submit_user=record.user_union_id,
                dentry_uuid=dentry_uuid,
                id_or_name=id_or_name,
                update_count=record.update_count,
                extends=record.extends,
            )

            task_list.append(work_task)

        self.logger.info(f"成功构建任务列表，共 {len(task_list)} 个任务")
        return task_list

    def _query_update_table_one(self) -> Optional[Dict[str, Any]]:
        """
        查询待更新的表信息 - 参考 old/xhs_detail_updater.py 的 __query_update_table_one 方法

        Returns:
            表信息字典
        """
        try:
            response = self.service_client.query_table_to_update()
            if not response:
                self.logger.warning("查询任务列表失败：响应为空")
                return None

            # 检查响应是否成功
            if not self.service_client.is_response_success(response):
                self.logger.error(f"查询任务列表失败：{response.get('msg', '未知错误')}")
                return None

            # 构造数据表
            table_info = response.get("result", {})

            if not table_info:
                self.logger.info("没有待更新的任务")
                return None

            self.logger.info(f"成功获取任务: {table_info}")
            return table_info

        except Exception as e:
            self.logger.error(f"查询任务列表异常: {e}")
            return None

    def _query_tasks_rows(
        self, platform: str = "all", limit: int = 100, record_id: str = None
    ) -> Optional[List[WorkDetailTaskModel]]:
        """
        直接从服务端获取任务数据 - 参考 old/xhs_detail_updater.py 的 __query_tasks_rows 方法

        Args:
            platform: 平台类型
            limit: 限制数量
            record_id: 批次记录ID

        Returns:
            任务列表
        """
        try:
            response = self.service_client.query_tasks_rows(platform=platform, limit=limit)
            if not response:
                self.logger.warning("查询任务列表失败：响应为空")
                return []

            # 检查响应是否成功
            if not self.service_client.is_response_success(response):
                self.logger.error(f"查询任务列表失败：{response.get('msg', '未知错误')}")
                return []

            # 构造任务列表
            task_data_list = response.get("result", [])

            if not task_data_list:
                self.logger.info("没有找到待更新的任务")
                return []

            task_list = []
            for task in task_data_list:
                work_url = task.get("workUrl")
                if not work_url:
                    continue

                table_info = task.get("tableInfo", {})
                dentry_uuid = table_info.get("dentry_uuid")
                id_or_name = table_info.get("id_or_name")

                result = self._extract_task_item(work_url=work_url)
                if not result:
                    continue

                platform, work_id = result
                if not platform or not work_id:
                    continue

                work_task = WorkDetailTaskModel(
                    id=None,
                    work_url=work_url,
                    platform=platform,
                    work_id=work_id,
                    row_id=task.get("rowId"),
                    submit_user=task.get("submitUser"),
                    dentry_uuid=dentry_uuid,
                    id_or_name=id_or_name,
                    update_count=task.get("updateCount", 0),
                    extends=task.get("extends"),
                )

                task_list.append(work_task)

            self.logger.info(f"成功获取任务列表，共 {len(task_list)} 个任务")
            return task_list

        except Exception as e:
            self.logger.error(f"查询任务列表异常: {e}")
            return []

    def _extract_task_item(self, work_url: str):
        """
        提取链接内信息：链接转化， work_id 提取， platform 信息提取
        参考 old/xhs_detail_updater.py 的 __extract_task_item 方法

        Args:
            work_url: 作品链接

        Returns:
            (platform, work_id) 或 None
        """
        if not work_url:
            self.logger.error("work_url为空")
            return None

        origin_url = work_url
        normal_url = origin_url

        self.logger.info(f"原始 work_url: {work_url}")

        # 分享链接确认
        if UrlUtils.check_short_url(normal_url):
            normal_url = self._url_redirection(normal_url)

        if not normal_url:
            self.logger.error(f"链接转化失败，短链转长连接失败 :{work_url}")
            return None

        # 去空格
        normal_url = normal_url.strip()

        # 提取平台信息
        platform = UrlUtils.select_platform(normal_url)

        if not platform:
            self.logger.error(f"format fail message :{origin_url}")
            return None

        if platform == "xhs":
            work_id = UrlUtils.get_xhs_work_id(normal_url)
        else:
            work_id = UrlUtils.get_dy_work_id(normal_url)

        return platform, work_id

    def _url_redirection(self, work_url: str) -> Optional[str]:
        """
        对短链的处理

        Args:
            work_url: 短链接

        Returns:
            长链接或None
        """
        try:
            response = requests.get(work_url, allow_redirects=True, timeout=30)
            return response.url
        except Exception as e:
            self.logger.error(f"短链转换失败: {e}")
            return None


class DataHandler(BaseHandler):

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self.refresh_repository = XhsWorkDetailRefreshRepository()
        # DataFetcher 需要在 handle 方法中初始化，因为需要 notifier

    def handle(self, context: PipelineContext) -> PipelineContext:
        """
        处理数据获取逻辑

        Args:
            context: Pipeline上下文

        Returns:
            更新后的Pipeline上下文
        """
        try:
            self.logger.info(f"🔍 开始获取数据，客户: {context.client_name}")

            # 初始化 DataFetcher (需要 notifier)
            data_fetcher = DataFetcher(
                client_config=self.client_config, logger=self.logger, notifier=context.notifier
            )

            # 获取任务列表
            task_list = data_fetcher.fetch_data(
                record_id=context.batch_id, data_source_type=context.data_source_type
            )

            if not task_list:
                self.logger.info("⚠️ 没有找到待更新的任务")
                context.task_list = []
                return self.handle_next(context)

            # 收集转换失败的URL
            context.error_format_url_list.extend(data_fetcher.covert_fail_queue)

            # 插入任务到数据库
            self._insert_tasks_to_db_batch(context, task_list)

            # 更新上下文
            context.task_list = task_list

            self.logger.info(f"✅ 成功获取 {len(context.task_list)} 个任务进行处理")
            return self.handle_next(context)

        except Exception as e:
            self.logger.error(f"❌ 数据获取失败: {e}")
            context.add_error(f"数据获取失败: {str(e)}")
            raise DataFetchException(f"Failed to fetch data: {str(e)}")

    def _insert_tasks_to_db_batch(
        self, context: PipelineContext, tasks: List[WorkDetailTaskModel]
    ) -> Optional[int]:
        """
        将任务插入到数据库

        Args:
            tasks: 任务列表
        """

        try:
            _task_repo_model_list: List[WorkDetailRefreshTask] = []
            for task in tasks:
                _task_repo_model = self._covert_task_to_repo_model(context, task)
                if not _task_repo_model:
                    continue

                _task_repo_model_list.append(_task_repo_model)
            task_num: int = self.refresh_repository.batch_insert_refresh_tasks(
                _task_repo_model_list
            )
            self.logger.info(f"=== 成功插入任务 {task_num} 条 ===")
            return task_num
        except Exception as e:
            self.logger.error(f"插入任务到数据库异常: {e}")
            raise

    def _covert_task_to_repo_model(
        self, context: PipelineContext, task: WorkDetailTaskModel
    ) -> Optional[WorkDetailRefreshTask]:
        """
        task model 转为数据存储
        :param task:
        :return:
        """
        try:
            return WorkDetailRefreshTask(
                id=None,
                record_id=context.batch_id,
                user_id=context.user_id,
                work_url=task.work_url,
                work_id=task.work_id,
                platform=task.platform,
                row_id=task.row_id,
                table_info=json.dumps(
                    {"id_or_name": task.id_or_name, "dentry_uuid": task.dentry_uuid}
                ),
            )
        except Exception as e:
            self.logger.error(f"转换任务失败: {task}, 错误: {e}")
            return None
