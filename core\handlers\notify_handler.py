"""
通知逻辑Handler - 负责基于阈值规则发送通知
"""

import json
from typing import List, Dict, Any, Tuple

from config.client_config import ClientConfig
from dao.repository.work_detail_task_notify_record_repository import WorkDetailTaskNotifyRecordRepository
from .base_handler import BaseHandler
from ..exceptions import NotificationException
from ..pipeline_context import PipelineContext
from models.work_models import WorkDetailTaskModel


class NotifyHandler(BaseHandler):
    """
    通知逻辑处理器基类

    职责：
    - 检查作品是否达到通知阈值
    - 发送通知给相关人员
    - 记录通知历史
    """

    def __init__(self, client_config: ClientConfig, logger_):
        super().__init__(client_config, logger_)
        self.logger_ = logger_
        self.task_notify_record_repo = WorkDetailTaskNotifyRecordRepository()
        self.dingtalk_notifier = None  # 将在运行时从context中获取


    def handle(self, context: PipelineContext) -> PipelineContext:
        """
        处理通知逻辑

        Args:
            context: Pipeline上下文

        Returns:
            更新后的Pipeline上下文
        """
        try:
            self.logger.info(f"Starting notification process for {len(context.task_list)} works")

            # 从context中获取钉钉通知器
            self.dingtalk_notifier = context.notifier

            notification_results = []
            notifications_sent = 0

            for work in context.task_list:
                try:
                    result = self._process_work_notification(work, context)
                    notification_results.append(result)

                    if result.get("sent", False):
                        notifications_sent += 1

                except Exception as e:
                    self.logger.error(f"Failed to process notification for {work.work_id}: {e}")
                    notification_results.append(
                        {"work_id": work.work_id, "success": False, "sent": False, "error": str(e)}
                    )

            self.logger.info(
                f"Notification process completed: {notifications_sent} notifications sent"
            )

            # 传递给下一个处理器
            return self.handle_next(context)

        except Exception as e:
            self.logger.error(f"Notification process failed: {e}")
            raise NotificationException(f"Failed to process notifications: {str(e)}")

    def _process_work_notification(self, work: WorkDetailTaskModel, context: PipelineContext) -> Dict[str, Any]:
        """
        处理单个作品的通知逻辑

        Args:
            work: 作品任务模型
            context: Pipeline上下文

        Returns:
            通知处理结果
        """
        try:
            # 获取用户ID和公司信息
            user_id = context.user_id
            company = context.client_name

            # 获取提交用户列表
            submit_user_list = work.submit_user if work.submit_user else []

            # 调用原有的通知逻辑
            result = self._notify_submit_user(user_id, work, submit_user_list, company)

            return {
                "work_id": work.work_id,
                "success": True,
                "sent": result.get("sent", False),
                "threshold_reached": result.get("threshold_reached", False),
                "message": result.get("message", "")
            }

        except Exception as e:
            self.logger.error(f"Failed to process notification for work {work.work_id}: {e}")
            return {
                "work_id": work.work_id,
                "success": False,
                "sent": False,
                "error": str(e)
            }

    def _notify_submit_user(self, user_id: int, work: WorkDetailTaskModel, submit_user_list: List[Dict], company: str) -> Dict[str, Any]:
        """
        通知提交用户（从原有代码移植）

        Args:
            user_id: 用户ID
            work: 作品任务模型
            submit_user_list: 提交用户列表
            company: 公司名称

        Returns:
            通知结果
        """
        try:
            work.threshold = "否"
            _work_detail = work.author_work

            if not submit_user_list or len(submit_user_list) == 0:
                self.logger_.info(f"notify user is null {user_id}")
                return {"sent": False, "threshold_reached": False, "message": "No submit users"}

            is_over_threshold, msg = self._check_is_threshold(company=company, work=work)

            if is_over_threshold:
                work_id = work.work_id
                work.threshold = "是"

                # 记录通知
                self.task_notify_record_repo.insert(
                    user_id,
                    "",
                    json.dumps(submit_user_list),
                    work_id,
                    work.platform
                )

                # 发送通知给每个用户
                for submit_user in submit_user_list:
                    try:
                        union_id = submit_user.get("unionId")
                        self.logger_.info(f"start notify union_id: {union_id} and msg: {msg}")
                        if self.dingtalk_notifier and union_id:
                            self.dingtalk_notifier.send_message(union_id, msg)
                    except Exception as ee:
                        self.logger_.error(f"Failed to send message to {union_id}: {ee}")

                return {
                    "sent": True,
                    "threshold_reached": True,
                    "message": msg,
                    "notified_users": len(submit_user_list)
                }
            else:
                return {"sent": False, "threshold_reached": False, "message": "Threshold not reached"}

        except Exception as e:
            self.logger_.error(f"Error in _notify_submit_user: {e}")
            return {"sent": False, "threshold_reached": False, "error": str(e)}

    def _check_is_threshold(self, company: str, work: WorkDetailTaskModel) -> Tuple[bool, str]:
        """
        检查是否达到阈值（从原有代码移植）

        Args:
            company: 公司名称
            work: 作品任务模型

        Returns:
            (是否达到阈值, 通知消息)
        """
        _author_work = work.author_work

        if not _author_work:
            return False, ""

        if company == "水云兔":
            like_count = _author_work.like_count
            if like_count >= 50:
                return True, f"作品阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 作品链接={work.work_url}"
            return False, ""

        elif company == "淘天":
            threshold = 50
            if _author_work.like_count > threshold:
                return True, f"1688舆情小助手-风险发酵提醒：工单编号{work.extends}，点赞数已达到{threshold}，请及时处理，风险链接：{work.work_url} "
            if _author_work.comment_count > threshold:
                return True, f"1688舆情小助手-风险发酵提醒：工单编号{work.extends}，评论数已达到{threshold}，请及时处理，风险链接：{work.work_url} "
            if _author_work.share_count > threshold:
                return True, f"1688舆情小助手-风险发酵提醒：工单编号{work.extends}，分享数已达到{threshold}，请及时处理，风险链接：{work.work_url} "
            if _author_work.collect_count > threshold:
                return True, f"1688舆情小助手-风险发酵提醒：工单编号{work.extends}，收藏数已达到{threshold}，请及时处理，风险链接：{work.work_url} "

        elif company == "告趣":
            threshold = 200
            if _author_work.like_count > threshold:
                return True, f"告趣小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 点赞数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.comment_count > threshold:
                return True, f"告趣小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 评论数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.share_count > threshold:
                return True, f"告趣小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 分享数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.collect_count > threshold:
                return True, f"告趣小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 收藏数已达到{threshold}，作品链接={work.work_url}"

        elif company == "云鸥":
            threshold = 50
            if _author_work.like_count > threshold:
                return True, f"云鸥小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 点赞数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.comment_count > threshold:
                return True, f"云鸥小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 评论数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.share_count > threshold:
                return True, f"云鸥小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 分享数已达到{threshold}，作品链接={work.work_url}"
            if _author_work.collect_count > threshold:
                return True, f"云鸥小助手-阈值通知：标题={_author_work.title}, 作者={_author_work.author_name}, 收藏数已达到{threshold}，作品链接={work.work_url}"

        return False, ""
