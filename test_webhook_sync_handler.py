#!/usr/bin/env python3
"""
测试 WebhookSyncHandler 的实现 - 简化版本
"""

import json
import logging
from dataclasses import dataclass
from typing import List, Dict, Any


@dataclass
class MockAuthorWork:
    """模拟作者作品数据"""

    title: str = "测试作品标题"
    platform: str = "xhs"
    author_name: str = "测试作者"
    author_url: str = "https://example.com/author"
    content: str = "测试内容"
    publish_time: str = "2025-01-01 12:00:00"
    like_count: int = 100
    comment_count: int = 20
    share_count: int = 10
    collect_count: int = 5


@dataclass
class MockWorkDetailTaskModel:
    """模拟作品任务模型"""

    work_id: str = "test_work_123"
    work_url: str = "https://example.com/work/123"
    platform: str = "xhs"
    row_id: str = "test_row_123"
    dentry_uuid: str = "test_dentry_uuid"
    id_or_name: str = "基础数据表"
    threshold: str = "否"
    update_count: int = 1
    is_exist: int = 1
    author_work: MockAuthorWork = None
    submit_user: List[Dict] = None

    def __post_init__(self):
        if self.author_work is None:
            self.author_work = MockAuthorWork()
        if self.submit_user is None:
            self.submit_user = [{"unionId": "test_union_id_123"}]


class MockClientConfig:
    """模拟客户端配置"""

    def __init__(self):
        self.bitable = {"webhook": "https://example.com/webhook"}
        self.data_sync = {"batch_size": 2}


class MockPipelineContext:
    """模拟Pipeline上下文"""

    def __init__(self):
        self.task_list = [
            MockWorkDetailTaskModel(work_id="work_1", row_id="row_1"),
            MockWorkDetailTaskModel(work_id="work_2", row_id="row_2"),
            MockWorkDetailTaskModel(work_id="work_3", row_id="row_3"),
        ]
        self.user_id = 123
        self.client_name = "水云兔"
        self.successful_updates = 0
        self.failed_updates = 0
        self.errors = []

    def add_error(self, error_message: str):
        self.errors.append(error_message)


def test_webhook_sync_handler():
    """测试 WebhookSyncHandler 的基本功能"""

    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # 创建模拟对象
    client_config = MockClientConfig()
    context = MockPipelineContext()

    # 创建处理器
    handler = WebhookSyncHandler(client_config, logger)

    print("=== 测试 WebhookSyncHandler ===")
    print(f"批次大小: {handler.batch_size}")
    print(f"Webhook URL: {handler.webhook_url}")
    print(f"任务数量: {len(context.task_list)}")

    # 测试数据格式化
    print("\n=== 测试数据格式化 ===")

    # 测试水云兔格式
    syt_data = handler._convert_syt_format(context.task_list, "test_dentry", "test_table")
    print(f"水云兔格式: {syt_data}")

    # 测试朱栈科技格式
    zz_data = handler._convert_zz_format(context.task_list)
    print(f"朱栈科技格式: {zz_data}")

    # 测试 webhook 数据格式化
    webhook_data = handler._format_webhook_data(
        context.task_list, "test_dentry", "test_table", "水云兔"
    )
    print(f"格式化的 webhook 数据: {webhook_data}")

    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    test_webhook_sync_handler()
