"""
责任链执行器 - 负责执行责任链处理流程

该模块提供了责任链的执行管理，包括：
1. 责任链的执行控制
2. 错误处理和日志记录
3. 执行时间统计
4. 上下文状态管理
"""

import logging
import time
from typing import Optional

from .handlers.base_handler import BaseHandler
from .pipeline_context import PipelineContext
from .exceptions import HandlerException


class ChainExecutor:
    """
    责任链执行器
    
    负责管理和执行责任链的处理流程，提供统一的执行接口和错误处理。
    """
    
    def __init__(self, first_handler: BaseHandler, logger: logging.Logger):
        """
        初始化责任链执行器
        
        Args:
            first_handler: 责任链的第一个处理器
            logger: 日志记录器
        """
        self.first_handler = first_handler
        self.logger = logger
        
    def execute(self, context: PipelineContext) -> PipelineContext:
        """
        执行责任链处理流程
        
        Args:
            context: Pipeline上下文
            
        Returns:
            处理后的Pipeline上下文
            
        Raises:
            HandlerException: 当处理过程中发生错误时
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"🚀 开始执行责任链处理，客户: {context.client_name}")
            self.logger.info(f"📋 批次ID: {context.batch_id}")
            
            # 执行责任链
            result_context = self.first_handler.execute_with_logging(context)
            
            execution_time = time.time() - start_time
            
            # 记录执行结果
            self._log_execution_summary(result_context, execution_time)
            
            return result_context
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ 责任链执行失败: {e}")
            self.logger.error(f"⏱️ 执行时间: {execution_time:.2f}秒")
            
            # 确保上下文中记录了错误
            context.add_error(f"责任链执行失败: {str(e)}")
            
            raise HandlerException(f"Chain execution failed: {str(e)}") from e
    
    def _log_execution_summary(self, context: PipelineContext, execution_time: float):
        """
        记录执行摘要
        
        Args:
            context: 处理后的上下文
            execution_time: 执行时间（秒）
        """
        summary = context.to_summary()
        
        self.logger.info("🎉 责任链执行完成")
        self.logger.info(f"⏱️ 总执行时间: {execution_time:.2f}秒")
        self.logger.info("📊 执行摘要:")
        self.logger.info(f"  - 客户名称: {summary['client_name']}")
        self.logger.info(f"  - 批次ID: {summary['batch_id']}")
        self.logger.info(f"  - 数据源类型: {summary['data_source_type']}")
        self.logger.info(f"  - 总任务数: {summary['total_tasks']}")
        self.logger.info(f"  - 成功更新: {summary['successful_updates']}")
        self.logger.info(f"  - 失败更新: {summary['failed_updates']}")
        self.logger.info(f"  - 错误数量: {summary['total_errors']}")
        self.logger.info(f"  - 格式错误URL: {summary['error_format_urls']}")
        self.logger.info(f"  - 更新失败URL: {summary['failed_update_urls']}")
        
        # 如果有错误，记录详细信息
        if context.errors:
            self.logger.warning("⚠️ 处理过程中发生的错误:")
            for i, error in enumerate(context.errors, 1):
                self.logger.warning(f"  {i}. {error}")
    
    def validate_chain(self) -> bool:
        """
        验证责任链的完整性
        
        Returns:
            True if chain is valid, False otherwise
        """
        try:
            current_handler = self.first_handler
            handler_count = 0
            handler_names = []
            
            while current_handler:
                handler_count += 1
                handler_names.append(current_handler.__class__.__name__)
                
                # 防止无限循环
                if handler_count > 10:
                    self.logger.error("❌ 责任链过长，可能存在循环引用")
                    return False
                
                current_handler = current_handler.next_handler
            
            self.logger.info(f"✅ 责任链验证通过，共 {handler_count} 个处理器")
            self.logger.info(f"📋 处理器链: {' -> '.join(handler_names)}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 责任链验证失败: {e}")
            return False
    
    def get_chain_info(self) -> dict:
        """
        获取责任链信息
        
        Returns:
            责任链信息字典
        """
        handlers = []
        current_handler = self.first_handler
        
        while current_handler:
            handlers.append({
                "name": current_handler.__class__.__name__,
                "handler_name": current_handler.handler_name
            })
            current_handler = current_handler.next_handler
        
        return {
            "total_handlers": len(handlers),
            "handlers": handlers,
            "is_valid": self.validate_chain()
        }


class SimpleChainExecutor:
    """
    简化的责任链执行器
    
    提供更简单的接口，适用于基本的责任链执行需求。
    """
    
    @staticmethod
    def execute_simple(first_handler: BaseHandler, context: PipelineContext) -> PipelineContext:
        """
        简单执行责任链
        
        Args:
            first_handler: 第一个处理器
            context: Pipeline上下文
            
        Returns:
            处理后的上下文
        """
        return first_handler.handle(context)
    
    @staticmethod
    def build_and_execute(handlers: list, context: PipelineContext) -> PipelineContext:
        """
        构建并执行责任链
        
        Args:
            handlers: 处理器列表
            context: Pipeline上下文
            
        Returns:
            处理后的上下文
        """
        if not handlers:
            raise ValueError("处理器列表不能为空")
        
        # 构建责任链
        for i in range(len(handlers) - 1):
            handlers[i].set_next(handlers[i + 1])
        
        # 执行责任链
        return handlers[0].handle(context)
