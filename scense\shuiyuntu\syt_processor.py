"""
水云兔客户的 Processor - 执行完整的数据处理流程

该 Processor 负责：
1. 初始化配置和依赖
2. 构建责任链
3. 执行数据获取 -> 更新 -> 通知 -> 同步的完整流程
4. 处理错误和日志记录

参考 old/xhs_detail_updater.py 的整体流程设计
"""

import logging
from typing import Optional

from config.client_config import ClientConfig
from core.handlers import (
    DataHandler,
    UpdateHandler,
    NotifyHandler,
    WebhookSyncHandler,
    PipelineContext,
)
from core.chain_executor import ChainExecutor
from jss_api_extend import DingTalkBitableNotifier, BaseDingTalkBitableNotifier
from utils.time_utils import TimeUtils
from common.env import Env




class ShuiyuntuProcessor:
    """
    水云兔客户的数据处理器

    负责执行完整的数据处理流程：
    1. 数据获取 (DataHandler)
    2. 作品更新 (UpdateHandler)
    3. 通知处理 (NotifyHandler)
    4. 数据同步 (WebhookSyncHandler)
    """

    def __init__(self, client_config: ClientConfig, logger):
        """
        初始化水云兔处理器

        Args:
            client_config: 客户配置
            logger: 日志记录器
        """
        self.client_config = client_config
        self.logger = logger
        self.client_name = client_config.name

        # 初始化钉钉通知器
        self.notifier = self._init_dingtalk_notifier()

        # 构建责任链
        self.chain_executor = self._build_processing_chain()

    def _init_dingtalk_notifier(self) -> BaseDingTalkBitableNotifier:
        """
        初始化钉钉多维表格通知器

        Returns:
            钉钉通知器实例
        """
        try:
            bitable_config = self.client_config.bitable

            notifier = BaseDingTalkBitableNotifier(
                app_key=bitable_config.get("app_key"),
                app_secret=bitable_config.get("app_secret"),
                agent_id=bitable_config.get("agent_id"),
                logger_=self.logger,
            )

            self.logger.info("✅ 钉钉通知器初始化成功")
            return notifier

        except Exception as e:
            self.logger.error(f"❌ 钉钉通知器初始化失败: {e}")
            raise

    def _build_processing_chain(self) -> ChainExecutor:
        """
        构建处理责任链

        Returns:
            责任链执行器
        """
        try:
            # 1. 数据获取处理器
            data_handler = DataHandler(self.client_config, self.logger)

            # 2. 作品更新处理器
            update_handler = UpdateHandler(self.client_config, self.logger)

            # 3. 通知处理器
            notify_handler = NotifyHandler(self.client_config, self.logger)

            # 4. 数据同步处理器 (使用 Webhook)
            sync_handler = WebhookSyncHandler(self.client_config, self.logger)

            # 构建责任链: 数据获取 -> 更新 -> 通知 -> 同步
            data_handler.set_next(update_handler).set_next(notify_handler).set_next(sync_handler)

            # 创建责任链执行器
            chain_executor = ChainExecutor(data_handler, self.logger)

            self.logger.info("✅ 处理责任链构建成功")
            return chain_executor

        except Exception as e:
            self.logger.error(f"❌ 责任链构建失败: {e}")
            raise

    def process(self, data_source_type: int = 1) -> dict:
        """
        执行完整的数据处理流程

        Args:
            data_source_type: 数据源类型 (1: 从服务器查询表信息, 2: 直接从服务端获取)

        Returns:
            处理结果摘要
        """
        batch_id = TimeUtils.get_current_ts("%Y%m%d%H%M%S")

        try:
            self.logger.info(f"🚀 开始执行水云兔数据处理流程，批次ID: {batch_id}")

            # 创建 Pipeline 上下文
            context = PipelineContext(
                client_name=self.client_name,
                batch_id=batch_id,
                user_id=self.client_config.user_info.get("user_id", 118),
                notifier=self.notifier,
                client_config=self.client_config,
                data_source_type=data_source_type,
            )

            # 执行责任链
            result_context = self.chain_executor.execute(context)

            # 生成处理摘要
            summary = result_context.to_summary()

            self.logger.info(f"🎉 水云兔数据处理流程完成")
            self.logger.info(f"📊 处理摘要: {summary}")

            return summary

        except Exception as e:
            self.logger.error(f"❌ 水云兔数据处理流程失败: {e}")
            return {
                "client_name": self.client_name,
                "batch_id": batch_id,
                "success": False,
                "error": str(e),
            }

    def process_with_bitable_source(self) -> dict:
        """
        使用多维表格数据源执行处理流程

        Returns:
            处理结果摘要
        """
        return self.process(data_source_type=1)

    def process_with_service_source(self) -> dict:
        """
        使用服务端数据源执行处理流程

        Returns:
            处理结果摘要
        """
        return self.process(data_source_type=2)


def create_shuiyuntu_processor(
    config_path: str = "config/clients/shuiyuntu.yaml",
) -> ShuiyuntuProcessor:
    """
    创建水云兔处理器的工厂函数

    Args:
        config_path: 配置文件路径

    Returns:
        水云兔处理器实例
    """
    from config.client_config import ClientConfig


    # 加载配置
    client_config = ClientConfig.from_yaml(config_path)

    # 创建处理器
    processor = ShuiyuntuProcessor(client_config, logger)

    return processor


# 便利函数
def run_shuiyuntu_process(
    config_path: str = "config/clients/shuiyuntu.yaml", data_source_type: int = 1
) -> dict:
    """
    运行水云兔数据处理流程的便利函数

    Args:
        config_path: 配置文件路径
        data_source_type: 数据源类型

    Returns:
        处理结果摘要
    """
    processor = create_shuiyuntu_processor(config_path)
    return processor.process(data_source_type)


if __name__ == "__main__":

    logger_ = Env().get_main_logger()
    # 示例用法
    print("🚀 启动水云兔数据处理流程...")

    try:
        # 使用多维表格数据源
        result = run_shuiyuntu_process(data_source_type=1)
        print(f"✅ 处理完成: {result}")

    except Exception as e:
        print(f"❌ 处理失败: {e}")
