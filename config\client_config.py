"""
Context classes for the work monitor system.

This module defines the context objects that carry data through
the chain of responsibility handlers.
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
import yaml
from pathlib import Path


@dataclass
class ClientConfig:
    """Configuration for a specific client."""

    name: str

    # 调度配置
    schedule: Dict[str, Any] = field(default_factory=dict)

    # 数据获取配置
    data_fetcher: Dict[str, Any] = field(default_factory=dict)

    # 作品更新配置
    work_updater: Dict[str, Any] = field(default_factory=dict)

    # 通知配置
    notification: Dict[str, Any] = field(default_factory=dict)

    # 用户信息配置
    user_info: Dict[str, Any] = field(default_factory=dict)

    # bitable配置
    bitable: Dict[str, Any] = field(default_factory=dict)

    # 数据同步配置
    data_sync: Dict[str, Any] = field(default_factory=dict)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "ClientConfig":
        """Create ClientConfig from dictionary."""
        return cls(
            name=data.get("name", ""),
            schedule=data.get("schedule", {}),
            data_fetcher=data.get("data_fetcher", {}),
            work_updater=data.get("work_updater", {}),
            notification=data.get("notification", {}),
            user_info=data.get("user_info", {}),
            bitable=data.get("bitable", {}),
            data_sync=data.get("data_sync", {}),
        )

    @classmethod
    def from_yaml(cls, yaml_path: str) -> "ClientConfig":
        """
        Create ClientConfig from YAML file.

        Args:
            yaml_path: Path to the YAML configuration file

        Returns:
            ClientConfig instance

        Raises:
            FileNotFoundError: If the YAML file doesn't exist
            yaml.YAMLError: If the YAML file is invalid
        """
        yaml_file = Path(yaml_path)

        if not yaml_file.exists():
            raise FileNotFoundError(f"Configuration file not found: {yaml_path}")

        try:
            with open(yaml_file, "r", encoding="utf-8") as f:
                data = yaml.safe_load(f)

            if not data:
                raise ValueError(f"Empty or invalid YAML file: {yaml_path}")

            return cls.from_dict(data)

        except yaml.YAMLError as e:
            raise yaml.YAMLError(f"Failed to parse YAML file {yaml_path}: {e}")
        except Exception as e:
            raise Exception(f"Failed to load configuration from {yaml_path}: {e}")
