"""
同步数据Handler - 负责将更新后的数据同步回数据源
"""

import json
import logging
import time
import requests
from typing import Dict, Any, List, Optional

from config.client_config import ClientConfig
from models.work_models import WorkDetailTaskModel
from .base_handler import BaseHandler
from ..exceptions import DataSyncException
from ..pipeline_context import PipelineContext
from utils.string_utils import StringUtils
from utils.time_utils import TimeUtils


class SyncHandler(BaseHandler):
    """
    数据同步处理器基类

    职责：
    - 将更新后的数据同步回数据源
    - 支持批量同步
    - 处理同步错误和重试
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self.batch_size = getattr(client_config, "data_sync", {}).get("batch_size", 100)

    def handle(self, context: PipelineContext) -> PipelineContext:
        """
        处理数据同步逻辑

        Args:
            context: Pipeline上下文

        Returns:
            更新后的Pipeline上下文
        """
        try:
            if not context.task_list:
                self.logger.info("⚠️ 工作列表为空，无需同步")
                return self.handle_next(context)

            self.logger.info(f"开始同步 {len(context.task_list)} 个任务")

            # 执行同步
            success_count, failed_count = self._perform_batch_sync(context)

            # 更新统计信息
            context.successful_updates += success_count
            context.failed_updates += failed_count

            self.logger.info(f"同步完成: 成功 {success_count}, 失败 {failed_count}")

            return self.handle_next(context)

        except Exception as e:
            self.logger.error(f"数据同步失败: {e}")
            context.add_error(f"数据同步失败: {str(e)}")
            return context

    def _perform_batch_sync(self, context: PipelineContext) -> tuple[int, int]:
        """
        执行批量同步

        Args:
            context: Pipeline上下文

        Returns:
            (成功数量, 失败数量)
        """
        raise NotImplementedError("Subclasses must implement _perform_batch_sync")


class DingTalkSyncHandler(SyncHandler):
    """
    钉钉多维表格同步处理器
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self._bitable_client = None

    def _perform_batch_sync(self, context: PipelineContext) -> tuple[int, int]:
        """执行钉钉批量同步"""
        success_count = 0
        failed_count = 0

        try:
            bitable_client = context.notifier
            field_mapping = getattr(self.client_config, "data_sync", {}).get("field_mapping", {})

            for work in context.task_list:
                try:
                    # 应用字段映射
                    mapped_data = self._apply_field_mapping(work, field_mapping)

                    # 更新记录
                    result = bitable_client.update_records(work.row_id, mapped_data)

                    if result:
                        self.logger.debug(f"Successfully synced work {work.work_id} to DingTalk")
                        success_count += 1
                    else:
                        self.logger.warning(f"Failed to sync work {work.work_id} to DingTalk")
                        failed_count += 1

                except Exception as e:
                    self.logger.error(f"Failed to sync work {work.work_id}: {e}")
                    failed_count += 1

        except Exception as e:
            self.logger.error(f"Failed to perform DingTalk batch sync: {e}")
            failed_count = len(context.task_list)

        return success_count, failed_count

    def _apply_field_mapping(
        self, work: WorkDetailTaskModel, field_mapping: Dict[str, str]
    ) -> Dict[str, Any]:
        """应用字段映射"""
        mapped_data = {}

        if not work.author_work:
            return mapped_data

        author_work = work.author_work

        # 基础字段映射
        field_values = {
            "title": getattr(author_work, "title", ""),
            "platform": getattr(author_work, "platform", ""),
            "author_name": getattr(author_work, "author_name", ""),
            "author_url": getattr(author_work, "author_url", ""),
            "content": getattr(author_work, "content", ""),
            "publish_time": getattr(author_work, "publish_time", ""),
            "like_count": getattr(author_work, "like_count", 0),
            "comment_count": getattr(author_work, "comment_count", 0),
            "share_count": getattr(author_work, "share_count", 0),
            "collect_count": getattr(author_work, "collect_count", 0),
            "update_count": getattr(work, "update_count", 0),
            "threshold": getattr(work, "threshold", "否"),
            "record_time": TimeUtils.get_current_ts(),
        }

        # 应用字段映射
        for target_field, source_field in field_mapping.items():
            if source_field in field_values:
                mapped_data[target_field] = field_values[source_field]

        return mapped_data

    def _get_bitable_client(self):
        """获取钉钉多维表格客户端"""
        if self._bitable_client is None:
            try:
                from jss_api_extend.dingtalk_bitable_factory import DingtalkBitableFactory

                bitable_config = self.client_config.bitable_config
                if not bitable_config:
                    raise DataSyncException("Missing bitable configuration")

                self._bitable_client = DingtalkBitableFactory.create_bitable_client(
                    api_token=self.client_config.user_info.get("api-token"),
                    dentry_uuid=bitable_config.get("dentryUuid"),
                    id_or_name=bitable_config.get("idOrName"),
                )

            except ImportError as e:
                self.logger.error(f"Failed to import DingTalk dependencies: {e}")
                raise DataSyncException("DingTalk dependencies not available")
            except Exception as e:
                self.logger.error(f"Failed to create DingTalk client: {e}")
                raise DataSyncException(f"DingTalk client creation failed: {str(e)}")

        return self._bitable_client


class WebhookSyncHandler(SyncHandler):
    """
    Webhook同步处理器

    参考 old/work_detail_processor.py 中的 _process_work_list 方法实现
    支持批量处理、按 dentry_uuid 分组、webhook 数据格式化和发送
    """

    def __init__(self, client_config: ClientConfig, logger: logging.Logger):
        super().__init__(client_config, logger)
        self.webhook_url = getattr(client_config, "bitable", {}).get("webhook", "")

    def _perform_batch_sync(self, context: PipelineContext) -> tuple[int, int]:
        """
        执行 Webhook 批量同步

        参考 old/work_detail_processor.py 的 _process_work_list 方法
        """
        if not context.task_list:
            self.logger.info("⚠️ 工作列表为空，无需处理")
            return 0, 0

        total_items = len(context.task_list)
        num_batches = (total_items + self.batch_size - 1) // self.batch_size

        total_success = 0
        total_failed = 0

        for batch_index in range(num_batches):
            # 计算当前批次的起止索引
            start_index = batch_index * self.batch_size
            end_index = min((batch_index + 1) * self.batch_size, total_items)

            # 获取当前批次数据
            current_batch = context.task_list[start_index:end_index]

            try:
                success_count, failed_count = self._send_sync_batch(context, current_batch)
                total_success += success_count
                total_failed += failed_count

            except Exception as e:
                self.logger.error(f"❌ 第{batch_index + 1}批处理失败: {str(e)}")
                total_failed += len(current_batch)

        return total_success, total_failed

    def _send_sync_batch(
        self, context: PipelineContext, work_list: List[WorkDetailTaskModel]
    ) -> tuple[int, int]:
        """
        发送同步批次

        参考 old/work_detail_processor.py 的 __send_sync_batch 方法
        """
        user_id = context.user_id

        # 按 dentry_uuid 分组
        grouped_works = {}
        for work in work_list:
            dentry_uuid = work.dentry_uuid

            if not dentry_uuid:
                self.logger.info(f"该条记录没有携带表信息: {work}")
                continue

            if dentry_uuid not in grouped_works:
                grouped_works[dentry_uuid] = []

            grouped_works[dentry_uuid].append(work)

        self.logger.info(f"group by dentry_uuid : {StringUtils.obj_2_json_string(grouped_works)}")

        total_success = 0
        total_failed = 0

        # 为每个 dentry_uuid 组发送 webhook
        for _dentry_uuid, works in grouped_works.items():
            if not works:
                continue

            _id_or_name = works[0].id_or_name

            self.logger.info(f"start sync bit table {_dentry_uuid}")

            try:
                # 格式化 webhook 数据
                webhook_data = self._format_webhook_data(
                    works, _dentry_uuid, _id_or_name, context.client_name
                )

                if webhook_data:
                    self.logger.info(f"webhook params :{webhook_data}")

                    # 发送 webhook 请求
                    success = self._send_webhook_request(webhook_data)

                    if success:
                        total_success += len(works)
                    else:
                        total_failed += len(works)
                else:
                    self.logger.warning(f"无法格式化 webhook 数据，跳过同步: {_dentry_uuid}")
                    total_failed += len(works)

            except Exception as e:
                self.logger.error(f"同步数据到 webhook 失败 {_dentry_uuid}: {e}")
                total_failed += len(works)

        return total_success, total_failed

    def _format_webhook_data(
        self,
        work_list: List[WorkDetailTaskModel],
        dentry_uuid: str,
        id_or_name: str,
        updater_name: str,
    ) -> Optional[Dict]:
        """
        格式化 webhook 数据

        参考 old/work_detail_processor.py 的 __update_webhook_formatter 方法
        支持不同客户的数据格式
        """
        self.logger.info(f"start build params :{updater_name}")

        try:
            if updater_name in ["水云兔", "淘天"]:
                return self._convert_syt_format(work_list, dentry_uuid, id_or_name)
            elif updater_name == "佳尔优优":
                return self._convert_jyy_format(work_list, dentry_uuid, id_or_name)
            elif updater_name == "朱栈科技":
                return self._convert_zz_format(work_list)
            elif updater_name in ["告趣", "云鸥"]:
                return self._convert_gq_format(work_list, dentry_uuid, id_or_name)
            else:
                # 默认格式 (使用水云兔格式)
                return self._convert_syt_format(work_list, dentry_uuid, id_or_name)

        except Exception as e:
            self.logger.error(f"构建 webhook 参数失败: {e}")
            return None

    def _convert_syt_format(
        self, work_list: List[WorkDetailTaskModel], dentry_uuid: str, id_or_name: str
    ) -> Optional[Dict]:
        """
        水云兔/淘天格式转换

        参考 old/work_detail_processor.py 的 __covert_syt 方法
        """
        update_records = []
        for work in work_list:
            if not work.author_work:
                continue

            author_work = work.author_work

            # 平台名称转换
            platform_name = "抖音"
            if author_work.platform == "xhs":
                platform_name = "小红书"
            elif author_work.platform == "dy":
                platform_name = "抖音"

            result = {
                "作品标题": getattr(author_work, "title", ""),
                "平台": platform_name,
                "作者": getattr(author_work, "author_name", ""),
                "作者主页": getattr(author_work, "author_url", ""),
                "正文": getattr(author_work, "content", ""),
                "作品发布时间": getattr(author_work, "publish_time", ""),
                "点赞数": getattr(author_work, "like_count", 0),
                "评论数": getattr(author_work, "comment_count", 0),
                "分享数": getattr(author_work, "share_count", 0),
                "收藏数": getattr(author_work, "collect_count", 0),
                "更新次数": getattr(work, "update_count", 0),
                "是否触发阈值": getattr(work, "threshold", "否"),
                "更新时间": TimeUtils.get_current_ts(),
            }

            update_records.append({"id": work.row_id, "fields": result})

        json_data = {
            "dentryUuid": dentry_uuid,
            "idOrName": id_or_name,
            "updateRecords": update_records,
        }
        return json_data

    def _convert_zz_format(self, work_list: List[WorkDetailTaskModel]) -> Optional[Dict]:
        """
        朱栈科技格式转换

        参考 old/work_detail_processor.py 的 __covert_zz 方法
        """
        webhook_params = []
        for work in work_list:
            if not work.author_work:
                continue

            author_work = work.author_work
            result = {
                "rowId": work.row_id,
                "workUrl": work.work_url,
                "likeCount": getattr(author_work, "like_count", 0),
                "commentCount": getattr(author_work, "comment_count", 0),
                "shareCount": getattr(author_work, "share_count", 0),
                "collectCount": getattr(author_work, "collect_count", 0),
            }
            webhook_params.append(result)

        json_data = {"data": webhook_params}
        return json_data

    def _convert_jyy_format(
        self, work_list: List[WorkDetailTaskModel], dentry_uuid: str, id_or_name: str
    ) -> Optional[Dict]:
        """
        佳尔优优格式转换

        参考 old/work_detail_processor.py 的 __covert_jyy 方法
        """
        update_records = []
        for work in work_list:
            if not work.author_work:
                continue

            author_work = work.author_work
            result = {
                "标题": getattr(author_work, "title", ""),
                "正文": getattr(author_work, "content", ""),
                "作者": getattr(author_work, "author_name", ""),
                "作者主页": getattr(author_work, "author_url", ""),
                "发布时间": getattr(author_work, "publish_time", ""),
                "点赞数": getattr(author_work, "like_count", 0),
                "评论数": getattr(author_work, "comment_count", 0),
                "分享数": getattr(author_work, "share_count", 0),
                "收藏数": getattr(author_work, "collect_count", 0),
                "更新时间": TimeUtils.get_current_ts(),
            }
            update_records.append({"id": work.row_id, "fields": result})

        json_data = {
            "dentryUuid": dentry_uuid,
            "idOrName": id_or_name,
            "updateRecords": update_records,
        }
        return json_data

    def _convert_gq_format(
        self, work_list: List[WorkDetailTaskModel], dentry_uuid: str, id_or_name: str
    ) -> Optional[Dict]:
        """
        告趣/云鸥格式转换

        参考 old/work_detail_processor.py 的 __covert_gq 方法
        """
        update_records = []
        for work in work_list:
            if not work.is_exist:
                # 作品不存在的情况
                result = {
                    "更新次数": getattr(work, "update_count", 0),
                    "作品是否可访问": "否",
                    "更新时间": TimeUtils.get_current_ts(),
                }
            else:
                # 作品存在的情况
                if not work.author_work:
                    continue

                author_work = work.author_work

                # 平台名称转换
                platform_name = "抖音"
                if author_work.platform == "xhs":
                    platform_name = "小红书"
                elif author_work.platform == "dy":
                    platform_name = "抖音"

                result = {
                    "作品标题": getattr(author_work, "title", ""),
                    "平台": platform_name,
                    "作者": getattr(author_work, "author_name", ""),
                    "作者主页": getattr(author_work, "author_url", ""),
                    "正文": getattr(author_work, "content", ""),
                    "作品发布时间": getattr(author_work, "publish_time", ""),
                    "点赞数": getattr(author_work, "like_count", 0),
                    "评论数": getattr(author_work, "comment_count", 0),
                    "分享数": getattr(author_work, "share_count", 0),
                    "收藏数": getattr(author_work, "collect_count", 0),
                    "更新次数": getattr(work, "update_count", 0),
                    "是否触发阈值": getattr(work, "threshold", "否"),
                    "更新时间": TimeUtils.get_current_ts(),
                    "作品是否可访问": "是",
                }

            update_records.append({"id": work.row_id, "fields": result})

        json_data = {
            "dentryUuid": dentry_uuid,
            "idOrName": id_or_name,
            "updateRecords": update_records,
        }
        return json_data

    def _send_webhook_request(self, webhook_data: Dict) -> bool:
        """
        发送 webhook 请求

        参考 jss_api_extend/client/dingtalk_linker.py 的 send_dingtalk_webhook_post 方法
        """
        if not self.webhook_url:
            self.logger.error("Webhook URL 未配置")
            return False

        headers = {"Content-Type": "application/json;charset=utf-8"}

        try:
            response = requests.post(self.webhook_url, json=webhook_data, headers=headers)
            response.raise_for_status()

            self.logger.info("Webhook 请求发送成功!")
            self.logger.info(f"响应状态码: {response.status_code}")
            self.logger.debug(f"响应内容: {response.text}")

            return True

        except requests.exceptions.HTTPError as http_err:
            self.logger.error(f"HTTP 错误: {http_err}")
            self.logger.error(
                f"响应状态码: {http_err.response.status_code if http_err.response else 'N/A'}"
            )
            self.logger.error(f"响应内容: {http_err.response.text if http_err.response else 'N/A'}")
            return False

        except requests.exceptions.RequestException as req_err:
            self.logger.error(f"Webhook 请求发送失败: {req_err}")
            return False

        except Exception as e:
            self.logger.error(f"发送 webhook 请求时发生未知错误: {e}")
            return False
