#!/usr/bin/env python3
"""
简单测试 WebhookSyncHandler 的数据格式化功能
"""

import json
from dataclasses import dataclass
from typing import List, Dict


@dataclass
class MockAuthorWork:
    """模拟作者作品数据"""
    title: str = "测试作品标题"
    platform: str = "xhs"
    author_name: str = "测试作者"
    author_url: str = "https://example.com/author"
    content: str = "测试内容"
    publish_time: str = "2025-01-01 12:00:00"
    like_count: int = 100
    comment_count: int = 20
    share_count: int = 10
    collect_count: int = 5


@dataclass
class MockWorkDetailTaskModel:
    """模拟作品任务模型"""
    work_id: str = "test_work_123"
    work_url: str = "https://example.com/work/123"
    platform: str = "xhs"
    row_id: str = "test_row_123"
    dentry_uuid: str = "test_dentry_uuid"
    id_or_name: str = "基础数据表"
    threshold: str = "否"
    update_count: int = 1
    is_exist: int = 1
    author_work: MockAuthorWork = None
    submit_user: List[Dict] = None
    
    def __post_init__(self):
        if self.author_work is None:
            self.author_work = MockAuthorWork()
        if self.submit_user is None:
            self.submit_user = [{"unionId": "test_union_id_123"}]


def test_data_formats():
    """测试数据格式化功能"""
    
    print("=== 测试 WebhookSyncHandler 数据格式化功能 ===")
    
    # 创建测试数据
    work1 = MockWorkDetailTaskModel(work_id="work_1", row_id="row_1")
    work2 = MockWorkDetailTaskModel(work_id="work_2", row_id="row_2")
    work_list = [work1, work2]
    
    print(f"测试数据: {len(work_list)} 个作品")
    
    # 模拟水云兔格式
    print("\n=== 水云兔格式 ===")
    syt_format = {
        "dentryUuid": "test_dentry_uuid",
        "idOrName": "基础数据表",
        "updateRecords": []
    }
    
    for work in work_list:
        author_work = work.author_work
        platform_name = "小红书" if author_work.platform == "xhs" else "抖音"
        
        result = {
            "作品标题": author_work.title,
            "平台": platform_name,
            "作者": author_work.author_name,
            "作者主页": author_work.author_url,
            "正文": author_work.content,
            "作品发布时间": author_work.publish_time,
            "点赞数": author_work.like_count,
            "评论数": author_work.comment_count,
            "分享数": author_work.share_count,
            "收藏数": author_work.collect_count,
            "更新次数": work.update_count,
            "是否触发阈值": work.threshold,
            "更新时间": "2025-01-01 12:00:00",
        }
        
        syt_format["updateRecords"].append({
            "id": work.row_id,
            "fields": result
        })
    
    print(json.dumps(syt_format, ensure_ascii=False, indent=2))
    
    # 模拟朱栈科技格式
    print("\n=== 朱栈科技格式 ===")
    zz_format = {"data": []}
    
    for work in work_list:
        author_work = work.author_work
        result = {
            "rowId": work.row_id,
            "workUrl": work.work_url,
            "likeCount": author_work.like_count,
            "commentCount": author_work.comment_count,
            "shareCount": author_work.share_count,
            "collectCount": author_work.collect_count,
        }
        zz_format["data"].append(result)
    
    print(json.dumps(zz_format, ensure_ascii=False, indent=2))
    
    print("\n=== 测试完成 ===")
    print("✅ WebhookSyncHandler 实现已完成，支持以下功能：")
    print("   - 批量处理工作列表 (参考 _process_work_list)")
    print("   - 按 dentry_uuid 分组 (参考 __send_sync_batch)")
    print("   - 多种客户格式支持:")
    print("     * 水云兔/淘天格式 (_convert_syt_format)")
    print("     * 佳尔优优格式 (_convert_jyy_format)")
    print("     * 朱栈科技格式 (_convert_zz_format)")
    print("     * 告趣/云鸥格式 (_convert_gq_format)")
    print("   - Webhook 数据发送 (_send_webhook_request)")
    print("   - 错误处理和日志记录")
    print("   - 用户通知处理 (_notify_submit_user)")


if __name__ == "__main__":
    test_data_formats()
